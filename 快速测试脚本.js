// 快速测试脚本 - 验证AutoJs6功能
console.log('=== AutoJs6 快速测试开始 ===');
console.log('测试时间: ' + new Date().toLocaleString());

// 1. 基本变量测试
console.log('\n1. 基本变量测试:');
var testNumber = 42;
var testString = 'Hello AutoJs6';
var testBoolean = true;
var testArray = [1, 2, 3, 4, 5];
var testObject = {name: 'AutoJs6', version: '1.0', working: true};

console.log('数字: ' + testNumber);
console.log('字符串: ' + testString);
console.log('布尔值: ' + testBoolean);
console.log('数组: [' + testArray.join(', ') + ']');
console.log('对象: ' + JSON.stringify(testObject));

// 2. 基本运算测试
console.log('\n2. 基本运算测试:');
var a = 10, b = 3;
console.log(a + ' + ' + b + ' = ' + (a + b));
console.log(a + ' - ' + b + ' = ' + (a - b));
console.log(a + ' * ' + b + ' = ' + (a * b));
console.log(a + ' / ' + b + ' = ' + (a / b));

// 3. 函数测试
console.log('\n3. 函数测试:');
function greet(name) {
    return 'Hello, ' + name + '!';
}

function calculate(x, y) {
    return {
        sum: x + y,
        product: x * y,
        difference: x - y
    };
}

console.log(greet('AutoJs6'));
var result = calculate(8, 3);
console.log('计算结果: ' + JSON.stringify(result));

// 4. 循环测试
console.log('\n4. 循环测试:');
console.log('for循环:');
for (var i = 1; i <= 3; i++) {
    console.log('  第 ' + i + ' 次循环');
}

console.log('while循环:');
var j = 1;
while (j <= 3) {
    console.log('  while第 ' + j + ' 次');
    j++;
}

// 5. 数组操作测试
console.log('\n5. 数组操作测试:');
var numbers = [1, 2, 3, 4, 5];
console.log('原数组: [' + numbers.join(', ') + ']');

var doubled = numbers.map(function(x) { return x * 2; });
console.log('翻倍后: [' + doubled.join(', ') + ']');

var evens = numbers.filter(function(x) { return x % 2 === 0; });
console.log('偶数: [' + evens.join(', ') + ']');

var sum = numbers.reduce(function(acc, x) { return acc + x; }, 0);
console.log('总和: ' + sum);

// 6. 错误处理测试
console.log('\n6. 错误处理测试:');
try {
    console.log('尝试正常操作...');
    var normalResult = 10 / 2;
    console.log('正常结果: ' + normalResult);
    
    console.log('尝试抛出错误...');
    throw new Error('这是一个测试错误');
} catch (e) {
    console.log('捕获到错误: ' + e.message);
} finally {
    console.log('finally块执行完成');
}

// 7. 时间测试
console.log('\n7. 时间测试:');
var now = new Date();
console.log('当前时间: ' + now.toString());
console.log('时间戳: ' + now.getTime());
console.log('年份: ' + now.getFullYear());
console.log('月份: ' + (now.getMonth() + 1));
console.log('日期: ' + now.getDate());

// 8. 字符串操作测试
console.log('\n8. 字符串操作测试:');
var text = '  AutoJs6 Script Engine  ';
console.log('原字符串: "' + text + '"');
console.log('去空格: "' + text.trim() + '"');
console.log('转大写: "' + text.toUpperCase() + '"');
console.log('转小写: "' + text.toLowerCase() + '"');
console.log('替换: "' + text.replace('Script', 'JavaScript') + '"');

// 9. 性能测试
console.log('\n9. 简单性能测试:');
var startTime = Date.now();
var count = 0;
for (var k = 0; k < 10000; k++) {
    count += k;
}
var endTime = Date.now();
console.log('10000次循环耗时: ' + (endTime - startTime) + 'ms');
console.log('计算结果: ' + count);

console.log('\n=== AutoJs6 快速测试完成 ===');
console.log('如果你看到这条消息，说明AutoJs6引擎工作正常！');
