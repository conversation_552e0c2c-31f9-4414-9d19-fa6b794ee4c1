# AutoJs6 脚本测试器 - 使用说明

## 新增功能

我已经为你的AutoJs6脚本测试器添加了多个测试按钮，从简单到复杂，可以全面测试AutoJs6的各种接口和功能。

## 测试按钮说明

### 第一行：基础测试
1. **基础测试** (绿色) - 测试基本的JavaScript语法和数据类型
   - 变量声明和赋值
   - 基本运算操作
   - 循环语句
   - 数据类型检查

2. **中级测试** (橙色) - 测试中等复杂度的JavaScript功能
   - 函数定义和调用
   - 数组操作 (map, filter, push, pop)
   - 对象操作
   - 错误处理 (try-catch)

3. **高级测试** (红色) - 测试高级JavaScript特性
   - 闭包
   - 原型链和继承
   - 异步操作模拟
   - 复杂数据结构处理

### 第二行：API测试
4. **Console API** (蓝色) - 测试控制台输出功能
   - console.log, info, warn, error
   - 格式化输出
   - 对象输出
   - 计时功能
   - 分组输出

5. **Utils API** (紫色) - 测试工具类API
   - 字符串处理工具
   - 数学计算工具
   - 日期时间工具
   - JSON序列化/反序列化

6. **System API** (灰色) - 测试系统相关API
   - 系统信息获取
   - 全局对象测试
   - 内存和性能测试
   - 错误类型测试
   - 垃圾回收测试

### 第三行：完整测试
7. **全部API测试** (棕色) - 运行所有API的综合测试
   - 自动运行多个测试用例
   - 统计测试结果
   - 显示成功率

8. **性能测试** (深橙色) - 测试JavaScript执行性能
   - 循环性能测试
   - 数组操作性能
   - 字符串操作性能
   - 对象访问性能
   - 函数调用性能

9. **压力测试** (粉色) - 高强度压力测试
   - 大量变量创建销毁
   - 大量函数调用
   - 大量字符串操作
   - 大量数组操作
   - 递归调用测试
   - JSON操作压力测试

## 日志功能增强

现在所有的脚本执行都会输出详细的日志信息，包括：

- 脚本开始执行时间
- 脚本执行时长
- 详细的错误信息
- 执行状态跟踪
- 性能统计信息

## 使用方法

1. **快速测试**: 直接点击任意测试按钮，系统会自动加载对应的测试脚本并执行
2. **查看结果**: 在界面上查看执行状态，在logcat中查看详细日志
3. **自定义脚本**: 仍然可以在文本框中输入自定义脚本并执行
4. **停止脚本**: 使用"停止所有脚本"按钮终止正在运行的脚本
5. **检查状态**: 使用"检查状态"按钮查看引擎运行状态

## 日志查看

使用以下命令查看详细日志：
```bash
adb logcat | grep -E "(SimpleAutoJsFragment|AutoJs6Module)"
```

## 测试建议

1. 从基础测试开始，逐步进行到高级测试
2. 观察每个测试的执行时间和结果
3. 如果某个测试失败，查看日志了解具体原因
4. 压力测试可能会消耗较多资源，建议在设备性能较好时运行
5. 全部API测试会运行较长时间，请耐心等待

## 故障排除

如果遇到问题：
1. 检查AutoJs6Module是否正确初始化
2. 查看logcat日志获取详细错误信息
3. 尝试重启应用
4. 确保设备有足够的内存和CPU资源

这个测试器现在可以全面测试AutoJs6的各种功能，帮助你快速发现和解决问题。
