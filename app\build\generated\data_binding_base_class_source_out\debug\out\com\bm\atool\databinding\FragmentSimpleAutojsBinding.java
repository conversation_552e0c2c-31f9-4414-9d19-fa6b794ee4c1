// Generated by view binder compiler. Do not edit!
package com.bm.atool.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.bm.atool.R;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentSimpleAutojsBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnCheckStatus;

  @NonNull
  public final Button btnExecuteScript;

  @NonNull
  public final Button btnStopAllScripts;

  @NonNull
  public final Button btnTestAdvanced;

  @NonNull
  public final Button btnTestAllAPIs;

  @NonNull
  public final Button btnTestBasic;

  @NonNull
  public final Button btnTestConsoleAPI;

  @NonNull
  public final Button btnTestIntermediate;

  @NonNull
  public final Button btnTestPerformance;

  @NonNull
  public final Button btnTestStress;

  @NonNull
  public final Button btnTestSystemAPI;

  @NonNull
  public final Button btnTestUtilsAPI;

  @NonNull
  public final TextInputEditText etScriptContent;

  @NonNull
  public final TextInputEditText etScriptName;

  @NonNull
  public final TextView tvRunningScripts;

  @NonNull
  public final TextView tvScriptStatus;

  private FragmentSimpleAutojsBinding(@NonNull ScrollView rootView, @NonNull Button btnCheckStatus,
      @NonNull Button btnExecuteScript, @NonNull Button btnStopAllScripts,
      @NonNull Button btnTestAdvanced, @NonNull Button btnTestAllAPIs, @NonNull Button btnTestBasic,
      @NonNull Button btnTestConsoleAPI, @NonNull Button btnTestIntermediate,
      @NonNull Button btnTestPerformance, @NonNull Button btnTestStress,
      @NonNull Button btnTestSystemAPI, @NonNull Button btnTestUtilsAPI,
      @NonNull TextInputEditText etScriptContent, @NonNull TextInputEditText etScriptName,
      @NonNull TextView tvRunningScripts, @NonNull TextView tvScriptStatus) {
    this.rootView = rootView;
    this.btnCheckStatus = btnCheckStatus;
    this.btnExecuteScript = btnExecuteScript;
    this.btnStopAllScripts = btnStopAllScripts;
    this.btnTestAdvanced = btnTestAdvanced;
    this.btnTestAllAPIs = btnTestAllAPIs;
    this.btnTestBasic = btnTestBasic;
    this.btnTestConsoleAPI = btnTestConsoleAPI;
    this.btnTestIntermediate = btnTestIntermediate;
    this.btnTestPerformance = btnTestPerformance;
    this.btnTestStress = btnTestStress;
    this.btnTestSystemAPI = btnTestSystemAPI;
    this.btnTestUtilsAPI = btnTestUtilsAPI;
    this.etScriptContent = etScriptContent;
    this.etScriptName = etScriptName;
    this.tvRunningScripts = tvRunningScripts;
    this.tvScriptStatus = tvScriptStatus;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentSimpleAutojsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentSimpleAutojsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_simple_autojs, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentSimpleAutojsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnCheckStatus;
      Button btnCheckStatus = ViewBindings.findChildViewById(rootView, id);
      if (btnCheckStatus == null) {
        break missingId;
      }

      id = R.id.btnExecuteScript;
      Button btnExecuteScript = ViewBindings.findChildViewById(rootView, id);
      if (btnExecuteScript == null) {
        break missingId;
      }

      id = R.id.btnStopAllScripts;
      Button btnStopAllScripts = ViewBindings.findChildViewById(rootView, id);
      if (btnStopAllScripts == null) {
        break missingId;
      }

      id = R.id.btnTestAdvanced;
      Button btnTestAdvanced = ViewBindings.findChildViewById(rootView, id);
      if (btnTestAdvanced == null) {
        break missingId;
      }

      id = R.id.btnTestAllAPIs;
      Button btnTestAllAPIs = ViewBindings.findChildViewById(rootView, id);
      if (btnTestAllAPIs == null) {
        break missingId;
      }

      id = R.id.btnTestBasic;
      Button btnTestBasic = ViewBindings.findChildViewById(rootView, id);
      if (btnTestBasic == null) {
        break missingId;
      }

      id = R.id.btnTestConsoleAPI;
      Button btnTestConsoleAPI = ViewBindings.findChildViewById(rootView, id);
      if (btnTestConsoleAPI == null) {
        break missingId;
      }

      id = R.id.btnTestIntermediate;
      Button btnTestIntermediate = ViewBindings.findChildViewById(rootView, id);
      if (btnTestIntermediate == null) {
        break missingId;
      }

      id = R.id.btnTestPerformance;
      Button btnTestPerformance = ViewBindings.findChildViewById(rootView, id);
      if (btnTestPerformance == null) {
        break missingId;
      }

      id = R.id.btnTestStress;
      Button btnTestStress = ViewBindings.findChildViewById(rootView, id);
      if (btnTestStress == null) {
        break missingId;
      }

      id = R.id.btnTestSystemAPI;
      Button btnTestSystemAPI = ViewBindings.findChildViewById(rootView, id);
      if (btnTestSystemAPI == null) {
        break missingId;
      }

      id = R.id.btnTestUtilsAPI;
      Button btnTestUtilsAPI = ViewBindings.findChildViewById(rootView, id);
      if (btnTestUtilsAPI == null) {
        break missingId;
      }

      id = R.id.etScriptContent;
      TextInputEditText etScriptContent = ViewBindings.findChildViewById(rootView, id);
      if (etScriptContent == null) {
        break missingId;
      }

      id = R.id.etScriptName;
      TextInputEditText etScriptName = ViewBindings.findChildViewById(rootView, id);
      if (etScriptName == null) {
        break missingId;
      }

      id = R.id.tvRunningScripts;
      TextView tvRunningScripts = ViewBindings.findChildViewById(rootView, id);
      if (tvRunningScripts == null) {
        break missingId;
      }

      id = R.id.tvScriptStatus;
      TextView tvScriptStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvScriptStatus == null) {
        break missingId;
      }

      return new FragmentSimpleAutojsBinding((ScrollView) rootView, btnCheckStatus,
          btnExecuteScript, btnStopAllScripts, btnTestAdvanced, btnTestAllAPIs, btnTestBasic,
          btnTestConsoleAPI, btnTestIntermediate, btnTestPerformance, btnTestStress,
          btnTestSystemAPI, btnTestUtilsAPI, etScriptContent, etScriptName, tvRunningScripts,
          tvScriptStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
