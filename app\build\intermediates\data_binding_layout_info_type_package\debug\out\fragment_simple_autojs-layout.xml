<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_simple_autojs" modulePackage="com.bm.atool" filePath="app\src\main\res\layout\fragment_simple_autojs.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_simple_autojs_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="307" endOffset="12"/></Target><Target id="@+id/tvScriptStatus" view="TextView"><Expressions/><location startLine="32" startOffset="12" endLine="39" endOffset="51"/></Target><Target id="@+id/tvRunningScripts" view="TextView"><Expressions/><location startLine="41" startOffset="12" endLine="47" endOffset="58"/></Target><Target id="@+id/etScriptName" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="59" startOffset="12" endLine="64" endOffset="43"/></Target><Target id="@+id/etScriptContent" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="76" startOffset="12" endLine="85" endOffset="58"/></Target><Target id="@+id/btnTestBasic" view="Button"><Expressions/><location startLine="107" startOffset="12" endLine="116" endOffset="39"/></Target><Target id="@+id/btnTestIntermediate" view="Button"><Expressions/><location startLine="118" startOffset="12" endLine="128" endOffset="39"/></Target><Target id="@+id/btnTestAdvanced" view="Button"><Expressions/><location startLine="130" startOffset="12" endLine="139" endOffset="39"/></Target><Target id="@+id/btnTestConsoleAPI" view="Button"><Expressions/><location startLine="150" startOffset="12" endLine="159" endOffset="39"/></Target><Target id="@+id/btnTestUtilsAPI" view="Button"><Expressions/><location startLine="161" startOffset="12" endLine="171" endOffset="39"/></Target><Target id="@+id/btnTestSystemAPI" view="Button"><Expressions/><location startLine="173" startOffset="12" endLine="182" endOffset="39"/></Target><Target id="@+id/btnTestAllAPIs" view="Button"><Expressions/><location startLine="193" startOffset="12" endLine="202" endOffset="39"/></Target><Target id="@+id/btnTestPerformance" view="Button"><Expressions/><location startLine="204" startOffset="12" endLine="214" endOffset="39"/></Target><Target id="@+id/btnTestStress" view="Button"><Expressions/><location startLine="216" startOffset="12" endLine="225" endOffset="39"/></Target><Target id="@+id/btnExecuteScript" view="Button"><Expressions/><location startLine="245" startOffset="12" endLine="253" endOffset="40"/></Target><Target id="@+id/btnStopAllScripts" view="Button"><Expressions/><location startLine="260" startOffset="16" endLine="269" endOffset="44"/></Target><Target id="@+id/btnCheckStatus" view="Button"><Expressions/><location startLine="271" startOffset="16" endLine="279" endOffset="44"/></Target></Targets></Layout>